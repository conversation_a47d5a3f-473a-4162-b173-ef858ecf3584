package net.summerfarm.wnc.application.inbound.controller.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.command.*;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaFenceDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaFenceAreaDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.*;
import net.summerfarm.wnc.application.service.fence.CustomFenceCommandService;
import net.summerfarm.wnc.application.service.fence.CustomFenceQueryService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 自定义围栏
 * date: 2025/8/26 16:30<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/custom-fence")
public class CustomFenceController {

    @Resource
    private CustomFenceCommandService customFenceCommandService;
    @Resource
    private CustomFenceQueryService customFenceQueryService;

    /**
     * 分页查询自定义区域列表
     * @param input 查询
     * @return 结果
     */
    @PostMapping("/query/page")
    //@RequiresPermissions(value = {"custom-fence:query-page"})
    public CommonResult<PageInfo<CustomCityAreaVO>> queryPage(@RequestBody @Validated CustomCityAreaQueryInput input) {
        PageInfo<CustomCityAreaVO> customCityAreaDTOPageList = customFenceQueryService.queryPage(input);
        return CommonResult.ok(customCityAreaDTOPageList);
    }

    /**
     * 查询围栏区域详情
     * @param input 查询
     * @return 结果
     */
    @PostMapping("/query/fence-area-detail")
    //@RequiresPermissions(value = {"custom-fence:fence-area-detail"})
    public CommonResult<CustomFenceDetailVO> queryFenceAreaDetail(@RequestBody @Validated CustomCityAreaFenceDetailQueryInput input) {
        CustomFenceDetailVO result = customFenceQueryService.queryFenceAreaDetail(input);
        return CommonResult.ok(result);
    }

    /**
     * 查询行政区域下所有自定义区域详情
     * @param input 查询
     * @return 结果
     */
    @PostMapping("/query/city-area-fence-area-detail")
    //@RequiresPermissions(value = {"custom-fence:city-area-fence-area-detail"})
    public CommonResult<CustomCityAreaFenceAreaDetailVO> queryCityAreaFenceAreaDetail(@RequestBody @Validated CustomCityAreaFenceAreaDetailQueryInput input) {
        CustomCityAreaFenceAreaDetailVO result = customFenceQueryService.queryCityAreaFenceAreaDetail(input);
        return CommonResult.ok(result);
    }

    /**
     * 新增自定义区域变更记录
     * @param input 新增
     * @return 结果
     */
    @PostMapping("/upsert/add-city-area-change-warehouse-record")
    //@RequiresPermissions(value = {"custom-fence:add-city-area-change-warehouse-record"})
    public CommonResult<CityAreaChangeWarehouseRecordAddVO> addCityAreaChangeWarehouseRecord(@RequestBody @Validated AddCityAreaChangeWarehouseRecordCommandInput input) {
        CityAreaChangeWarehouseRecordAddVO result = customFenceCommandService.addCityAreaChangeWarehouseRecord(input);
        return CommonResult.ok(result);
    }


    /**
     * 新增自定义围栏变更记录
     * @param input 新增
     * @return 结果
     */
    @PostMapping("/upsert/add-custom-fence-change-record")
    //@RequiresPermissions(value = {"custom-fence:add-custom-fence-change-record"})
    public CommonResult<AddCustomFenceChangeRecordVO> addCustomFenceChangeRecord(@RequestBody @Validated AddCustomFenceChangeRecordCommandInput input) {
        AddCustomFenceChangeRecordVO result = customFenceCommandService.addCustomFenceChangeRecord(input);
        return CommonResult.ok(result);
    }

    /**
     * 新增自定义区域
     * @param input 添加
     * @return 结果
     */
    @PostMapping("/upsert/add-custom-fence-area")
    //@RequiresPermissions(value = {"custom-fence:add-custom-fence-area"})
    public CommonResult<AddCustomFenceAreaVO> addCustomFenceArea(@RequestBody @Validated AddCustomFenceAreaCommandInput input) {
        AddCustomFenceAreaVO result = customFenceCommandService.addCustomFenceArea(input);
        return CommonResult.ok(result);
    }

    /**
     * 删除自定义围栏
     * @param input 删除
     * @return 结果
     */
    @PostMapping("/upsert/delete-custom-fence-change-record")
    //@RequiresPermissions(value = {"custom-fence:delete-custom-fence-change-record"})
    public CommonResult<Boolean> deleteCustomFenceChangeRecord(@RequestBody @Validated DelCustomFenceChangeRecordCommandInput input) {
        boolean result = customFenceCommandService.deleteCustomFenceChangeRecord(input);
        return CommonResult.ok(result);
    }

    /**
     * 删除自定义围栏区域
     * @param input 删除
     * @return 结果
     */
    @PostMapping("/upsert/delete-custom-fence-area-change-record")
    //@RequiresPermissions(value = {"custom-fence:delete-custom-fence-area-change-record"})
    public CommonResult<Boolean> deleteCustomFenceAreaChangeRecord(@RequestBody @Validated DelCustomFenceAreaChangeRecordCommandInput input) {
        boolean result = customFenceCommandService.deleteCustomFenceAreaChangeRecord(input);
        return CommonResult.ok(result);
    }

    /**
     * 更新自定义围栏
     * @param input 更新参数
     * @return 结果
     */
    @PostMapping("/upsert/update-custom-fence")
    //@RequiresPermissions(value = {"custom-fence:update-custom-fence"})
    public CommonResult<Boolean> updateCustomFence(@RequestBody @Validated UpdateCustomFenceChangeRecordCommandInput input) {
        boolean success = customFenceCommandService.updateCustomFenceChangeRecord(input);
        if (success) {
            return CommonResult.ok(true);
        } else {
            return CommonResult.fail();
        }
    }

    /**
     * 更新自定义区域
     * @param input 更新参数
     * @return 结果
     */
    @PostMapping("/upsert/update-custom-fence-area")
    //@RequiresPermissions(value = {"custom-fence:add-custom-fence-area"})
    public CommonResult<Boolean> updateCustomFenceArea(@RequestBody @Validated UpdateCustomFenceAreaCommandInput input) {
        // 调用服务层更新自定义围栏区域
        boolean success = customFenceCommandService.updateCustomFenceArea(input);
        if (success) {
            return CommonResult.ok(true);
        } else {
            return CommonResult.fail();
        }
    }
}
