package net.summerfarm.wnc.infrastructure.repository.fence;


import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncCityAreaChangeWarehouseRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncCityAreaChangeWarehouseRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncCityAreaChangeWarehouseRecordsQueryRepositoryImpl implements WncCityAreaChangeWarehouseRecordsQueryRepository {

    @Autowired
    private WncCityAreaChangeWarehouseRecordsMapper wncCityAreaChangeWarehouseRecordsMapper;


    @Override
    public PageInfo<WncCityAreaChangeWarehouseRecordsEntity> getPage(WncCityAreaChangeWarehouseRecordsQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WncCityAreaChangeWarehouseRecordsEntity> entities = wncCityAreaChangeWarehouseRecordsMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WncCityAreaChangeWarehouseRecordsEntity selectById(Long id) {
        return WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecordsEntity(wncCityAreaChangeWarehouseRecordsMapper.selectById(id));
    }


    @Override
    public List<WncCityAreaChangeWarehouseRecordsEntity> selectByCondition(WncCityAreaChangeWarehouseRecordsQueryParam param) {
        return WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecordsEntityList(wncCityAreaChangeWarehouseRecordsMapper.selectByCondition(param));
    }


    @Override
    public Map<Long, List<WncCityAreaChangeWarehouseRecordsEntity>> selectTaskIdMapByFenceChangeTaskIdsChangeStatus(List<Long> fenceChangeTaskIds, Integer changeStatus) {
        if(CollectionUtils.isEmpty(fenceChangeTaskIds) || changeStatus == null){
            return Collections.emptyMap();
        }

        WncCityAreaChangeWarehouseRecordsQueryParam param = new WncCityAreaChangeWarehouseRecordsQueryParam();
        param.setFenceChangeTaskIds(fenceChangeTaskIds);
        param.setChangeStatus(changeStatus);
        List<WncCityAreaChangeWarehouseRecords> cityAreaChangeWarehouseRecords = wncCityAreaChangeWarehouseRecordsMapper.selectByCondition(param);

        List<WncCityAreaChangeWarehouseRecordsEntity> cityAreaChangeWarehouseRecordsEntities = WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecordsEntityList(cityAreaChangeWarehouseRecords);

        if (!CollectionUtils.isEmpty(cityAreaChangeWarehouseRecordsEntities)) {
            return cityAreaChangeWarehouseRecordsEntities.stream().filter(e -> e.getFenceChangeTaskId() != null)
                    .collect(Collectors.groupingBy(WncCityAreaChangeWarehouseRecordsEntity::getFenceChangeTaskId));
        }

        return Collections.emptyMap();
    }

    @Override
    public List<WncCityAreaChangeWarehouseRecordsEntity> selectExecutableRecords(LocalDateTime preExeTime, Integer changeStatus, Integer areaDefinationType) {
        if(preExeTime == null || changeStatus == null){
            return Collections.emptyList();
        }
        return WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecordsEntityList(wncCityAreaChangeWarehouseRecordsMapper.selectExecutableRecords(preExeTime, changeStatus,areaDefinationType));
    }

    @Override
    public List<WncCityAreaChangeWarehouseRecordsEntity> selectByCityAreasChangeStage(String city, List<String> areas, WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus changeStatus) {
        if (StringUtils.isEmpty(city) || changeStatus == null) {
            return null;
        }
        WncCityAreaChangeWarehouseRecordsQueryParam param = new WncCityAreaChangeWarehouseRecordsQueryParam();
        param.setCity(city);
        param.setAreaList(areas);
        param.setChangeStatus(changeStatus.getValue());

        return WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecordsEntityList(wncCityAreaChangeWarehouseRecordsMapper.selectByCondition(param));
    }
}